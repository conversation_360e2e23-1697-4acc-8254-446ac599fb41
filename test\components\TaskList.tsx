import React, { useEffect } from 'react';
import {
  Alert,
  Platform,
  Text,
  View
} from 'react-native';
import { commonStyles } from '../styles';
import { Task } from '../types';
import { TaskItem } from './TaskItem';

interface TaskListProps {
  tasks: Task[];
  currentView: string;
  onEditTask: (task: Task) => void;
  toggleTaskCompletion: (taskId: string) => Promise<void> | void;
  deleteTask: (taskId: string) => Promise<void> | void;
}

export const TaskList: React.FC<TaskListProps> = ({
  tasks,
  currentView,
  onEditTask,
  toggleTaskCompletion,
  deleteTask,
}) => {
  // Debug logging
  // console.log('TaskList render - tasks count:', tasks.length);
  // if (tasks.length > 0) {
  //   console.log('First task:', tasks[0].title);
  // }

  const handleToggle = async (taskId: string) => {
    try {
      await toggleTaskCompletion(taskId);
    } catch (error) {
      // Platform-specific error handling
      if (Platform.OS === 'web') {
        window.alert('Failed to update task');
      } else {
        Alert.alert('Error', 'Failed to update task');
      }
    }
  };

  useEffect(() => {
    console.log(tasks);
  }, [tasks])

  const handleDelete = async (taskId: string) => {
    console.log('handleDelete called with taskId:', taskId);

    // Platform-specific confirmation dialog
    if (Platform.OS === 'web') {
      // Use browser confirm dialog for web
      const confirmed = window.confirm('Are you sure you want to delete this task?');

      if (confirmed) {
        console.log('Delete confirmed for taskId:', taskId);
        try {
          await deleteTask(taskId);
          console.log('Delete successful for taskId:', taskId);
        } catch (error) {
          console.error('Delete failed for taskId:', taskId, error);
          window.alert('Failed to delete task');
        }
      } else {
        console.log('Delete cancelled for taskId:', taskId);
      }
    } else {
      // Use React Native Alert for mobile apps
      Alert.alert(
        'Delete Task',
        'Are you sure you want to delete this task?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              console.log('Delete confirmed for taskId:', taskId);
              try {
                await deleteTask(taskId);
                console.log('Delete successful for taskId:', taskId);
              } catch (error) {
                console.error('Delete failed for taskId:', taskId, error);
                Alert.alert('Error', 'Failed to delete task');
              }
            },
          },
        ]
      );
    }
  };

  return (
    <View style={commonStyles.section}>
      <Text style={commonStyles.sectionTitle}>📋 To-Do List</Text>
      {tasks.length === 0 ? (
        <Text style={{ color: '#666', textAlign: 'center', padding: 20 }}>No tasks yet</Text>
      ) : (
        tasks.map((task) => (
          <TaskItem
            key={task.task_id || task.id}
            task={task}
            onToggle={() => handleToggle(task.task_id || task.id || '')}
            onDelete={() => handleDelete(task.task_id || task.id || '')}
            onEdit={() => onEditTask(task)}
          />
        ))
      )}
    </View>
  );
};
