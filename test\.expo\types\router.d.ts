/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../components/AddTaskModal`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useTasks`; params?: Router.UnknownInputParams; } | { pathname: `/../services/api`; params?: Router.UnknownInputParams; } | { pathname: `/../types/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/AddTaskModal`; params?: Router.UnknownOutputParams; } | { pathname: `/../hooks/useTasks`; params?: Router.UnknownOutputParams; } | { pathname: `/../services/api`; params?: Router.UnknownOutputParams; } | { pathname: `/../types/index`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/../components/AddTaskModal${`?${string}` | `#${string}` | ''}` | `/../hooks/useTasks${`?${string}` | `#${string}` | ''}` | `/../services/api${`?${string}` | `#${string}` | ''}` | `/../types/index${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/../components/AddTaskModal`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/useTasks`; params?: Router.UnknownInputParams; } | { pathname: `/../services/api`; params?: Router.UnknownInputParams; } | { pathname: `/../types/index`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
    }
  }
}
