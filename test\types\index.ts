// Types for the Todo List App

export type TaskType = 'item' | 'task' | 'event';

export interface Task {
  complete_by: any;
  duration_minutes: number;
  task_id: string;
  uid: string;
  title: string;
  description?: string;
  task_type: TaskType;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';

  // Scheduling fields
  due_date?: string | Date; // For Items (date) and Tasks (datetime)
  start_time?: string | Date; // For Events
  end_time?: string | Date; // For Events
  estimated_duration?: number; // For Tasks (minutes)
  flexibility: number; // 1-10 scale

  // Metadata
  created_at: string;
  updated_at: string;
  completed_at?: string | Date;
  is_recurring?: boolean;

  // Frontend-specific alias for convenience
  id?: string; // alias for task_id
  completed?: boolean; // computed from status
  startTime?: string; // computed from scheduled_time
  endTime?: string; // computed from scheduled_time + duration
  isRecurring?: boolean; // alias for is_recurring
  isFlexible?: boolean; // computed from flexibility
  notes?: string; // alias for description
}

export interface Goal {
  goal_id: string;
  uid: string;
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  target_date?: string; // ISO format
  categories: string[];
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export type Rank = 'pawn' | 'knight' | 'bishop' | 'rook' | 'queen' | 'king';

export interface User {
  uid: string;
  handle: string;
  first_name: string;
  last_name: string;
  email: string;
  rank: Rank;
  coins: number;
  date_of_birth?: string;
  created_at: string;
  updated_at: string;

  // Frontend-specific fields for compatibility
  avatar?: string;
}

export interface Friend {
  id: string;
  name: string;
  rank: number;
  avatar: string;
}

export interface GameStats {
  coins: number;
  cardEffects: string[];
  availablePacks: string[];
}

export type ViewType = 'today' | 'calendar';

export interface NewTaskForm {
  title: string;
  taskType: TaskType;
  notes: string;
  priority: 'low' | 'medium' | 'high';
  isRecurring: boolean;

  // Fields for 'item'
  dueDate?: Date;

  // Fields for 'task'
  completeBy?: Date;
  duration?: number; // in minutes
  flexibility?: number;

  // Fields for 'event'
  startTime?: Date;
  endTime?: Date;
}

export interface SettingsButtonLayout {
  x: number;
  y: number;
  width: number;
  height: number;
}
