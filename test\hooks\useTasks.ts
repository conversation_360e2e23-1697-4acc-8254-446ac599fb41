import { useCallback, useEffect, useState } from 'react';
import { api } from '../services/api';
import { NewTaskForm, Task } from '../types';

export const useTasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Transform backend task to frontend format
  const transformTask = (backendTask: any): Task => {
    // Basic mapping
    const task: Task = {
      task_id: backendTask.task_id,
      uid: backendTask.uid,
      title: backendTask.title,
      description: backendTask.description || '',
      task_type: backendTask.task_type,
      status: backendTask.status,
      priority: backendTask.priority,
      flexibility: backendTask.flexibility,
      created_at: backendTask.created_at,
      updated_at: backendTask.updated_at,
      is_recurring: backendTask.is_recurring,
      id: backendTask.task_id, // Alias for frontend use
    };

    // Add type-specific fields
    if (backendTask.due_date) task.due_date = backendTask.due_date;
    if (backendTask.start_time) task.start_time = backendTask.start_time;
    if (backendTask.end_time) task.end_time = backendTask.end_time;
    if (backendTask.estimated_duration) task.estimated_duration = backendTask.estimated_duration;

    return task;
  };

  // Load tasks from API
  const refetchTasks = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const backendTasks = await api.tasks.getTasks();
      const transformedTasks = backendTasks.map(transformTask);
      setTasks(transformedTasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load tasks');
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new task
  const createTask = useCallback(async (taskData: NewTaskForm) => {
    setLoading(true);
    setError(null);

    // Base payload for all task types
    const basePayload: any = {
      title: taskData.title,
      description: taskData.notes,
      priority: taskData.priority,
      is_recurring: taskData.isRecurring,
      task_type: taskData.taskType,
    };

    // Helper function to safely convert to ISO string
    const toISOString = (date: Date | string | undefined): string | undefined => {
      if (!date) return undefined;
      if (date instanceof Date) return date.toISOString();
      if (typeof date === 'string') return new Date(date).toISOString();
      return undefined;
    };

    // Add fields based on task type
    switch (taskData.taskType) {
      case 'item':
        if (taskData.dueDate) {
          basePayload.due_date = toISOString(taskData.dueDate);
        }
        break;
      case 'task':
        if (taskData.completeBy) {
          basePayload.due_date = toISOString(taskData.completeBy);
        }
        basePayload.estimated_duration = taskData.duration;
        basePayload.flexibility = taskData.flexibility;
        break;
      case 'event':
        if (taskData.startTime) {
          basePayload.start_time = toISOString(taskData.startTime);
        }
        if (taskData.endTime) {
          basePayload.end_time = toISOString(taskData.endTime);
        }
        break;
    }

    try {
      const result = await api.tasks.createTask(basePayload);
      console.log('Task created:', result);

      // Optimistically add the new task to the state
      const newTask = transformTask({
        ...basePayload,
        task_id: result.task_id,
        uid: 'demo_user_123', // Replace with actual user ID from auth context
        status: 'planned',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      setTasks(prevTasks => [newTask, ...prevTasks]);

      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create task');
      console.error('Error creating task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a task
  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    setLoading(true);
    setError(null);
    try {
      await api.tasks.updateTask(taskId, updates);
      
      // Update local state optimistically
      setTasks(
        prevTasks =>
          prevTasks.map(task =>
            (task.task_id || task.id) === taskId ? { ...task, ...updates } : task
          )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update task');
      console.error('Error updating task:', err);
      // Reload tasks to revert optimistic update
      await refetchTasks();
      throw err;
    } finally {
      setLoading(false);
    }
  }, [refetchTasks]);

  // Delete a task
  const deleteTask = useCallback(async (taskId: string) => {
    // Store the task to be deleted for potential revert
    const taskToDelete = tasks.find(task => (task.task_id || task.id) === taskId);

    // Optimistically remove from local state
    setTasks(prevTasks => prevTasks.filter(task => (task.task_id || task.id) !== taskId));

    try {
      await api.tasks.deleteTask(taskId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete task');
      console.error('Error deleting task:', err);
      // Revert the optimistic update on failure by re-adding the task
      if (taskToDelete) {
        setTasks(prevTasks => [...prevTasks, taskToDelete]);
      }
      throw err;
    }
  }, [tasks]);

  // Toggle task completion
  const toggleTaskCompletion = useCallback(async (taskId: string) => {
    // Find the current task to determine its current status
    const currentTask = tasks.find(task => (task.task_id || task.id) === taskId);
    if (!currentTask) return;

    console.log('Current task:', currentTask);
    console.log('Current task status:', currentTask.status);

    const isCurrentlyCompleted = currentTask.status === 'completed';
    const newStatus = isCurrentlyCompleted ? 'planned' : 'completed';

    console.log('isCurrentlyCompleted:', isCurrentlyCompleted);
    console.log('newStatus:', newStatus);

    // Optimistically update the UI
    setTasks(prevTasks => 
      prevTasks.map(task => 
        (task.task_id || task.id) === taskId 
          ? { ...task, status: newStatus }
          : task
      )
    );

    try {
      await updateTask(taskId, { status: newStatus });
    } catch (err) {
      // Revert the optimistic update on failure
      setTasks(prevTasks => 
        prevTasks.map(task => 
          (task.task_id || task.id) === taskId 
            ? { ...task, status: currentTask.status }
            : task
        )
      );
      setError(err instanceof Error ? err.message : 'Failed to toggle task completion');
      console.error('Error toggling task completion:', err);
      throw err;
    }
  }, [tasks, updateTask]);

  // Schedule a task using AI
  const scheduleTask = useCallback(async (taskId: string, preferredTime?: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.tasks.scheduleTask(taskId, preferredTime);
      console.log('Task scheduled:', result);
      
      // Reload tasks to get updated schedule
      await refetchTasks();
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to schedule task');
      console.error('Error scheduling task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [refetchTasks]);

  // Load tasks on mount
  useEffect(() => {
    refetchTasks();
  }, [refetchTasks]);

  return {
    tasks,
    isLoading,
    error,
    refetchTasks,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    scheduleTask,
  };
};
